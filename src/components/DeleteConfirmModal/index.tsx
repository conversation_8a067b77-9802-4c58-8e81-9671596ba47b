import React, { useEffect } from "react";
import { Modal } from "antd";

interface DeleteConfirmModalProps {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  title?: string;
  content?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  icon?: React.ReactNode;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  title = "确认删除账户?",
  content = "删除账户是永久性的。您的服务请求、帖子和评论等数据将被永久删除。",
  confirmText = "确认",
  cancelText = "取消",
  onConfirm,
  onCancel,
  loading = false,
  icon,
}) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isModalOpen) {
      const scrollY = window.scrollY;
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";
      document.body.style.overflow = "hidden";

      return () => {
        document.body.style.position = "";
        document.body.style.top = "";
        document.body.style.width = "";
        document.body.style.overflow = "";
        window.scrollTo(0, scrollY);
      };
    }
  }, [isModalOpen]);

  const handleCancel = () => {
    setIsModalOpen(false);
    onCancel && onCancel();
  };

  const handleConfirm = async () => {
    if (onConfirm) {
      await onConfirm();
    }
    setIsModalOpen(false);
  };

  const defaultIcon = (
    <div className="w-20 h-20 mx-auto mb-8 flex items-center justify-center">
      {/* Warning/Delete Icon - matches the screenshot */}
      <div className="w-20 h-20 bg-gray-600 rounded-full flex items-center justify-center">
        <svg
          className="w-10 h-10 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
          />
        </svg>
      </div>
    </div>
  );

  return (
    <Modal
      open={isModalOpen}
      onCancel={handleCancel}
      footer={null}
      closeIcon={null}
      centered
      width={400}
      styles={{
        content: {
          backgroundColor: "#1f222a",
          borderRadius: "16px",
          padding: "32px 24px",
        },
        body: {
          padding: 0,
        },
      }}
      maskStyle={{
        backgroundColor: "rgba(0, 0, 0, 0.8)",
      }}
    >
      <div className="text-center">
        {/* Icon */}
        {icon || defaultIcon}

        {/* Title */}
        <h2 className="text-white text-xl font-bold mb-4">{title}</h2>

        {/* Content */}
        <p className="text-gray-300 text-sm leading-relaxed mb-8 px-2">
          {content}
        </p>

        {/* Buttons */}
        <div className="space-y-3">
          {/* Confirm Button */}
          <button
            onClick={handleConfirm}
            disabled={loading}
            className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-4 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? "处理中..." : confirmText}
          </button>

          {/* Cancel Button */}
          <button
            onClick={handleCancel}
            disabled={loading}
            className="w-full bg-transparent border border-gray-600 hover:border-gray-500 text-white font-medium py-4 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {cancelText}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmModal;
