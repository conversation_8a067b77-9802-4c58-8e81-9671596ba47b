import React, { useMemo } from "react";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";
import {
  personalInformationPath,
  passwordSecurityPath,
  myPreferencesPath,
} from "components/StartAuthentication/constant";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";

const Account = () => {
  // Account menu items configuration
  const accountMenuItems = useMemo(
    () => [
      {
        id: "personal_information",
        titleKey: "account_menu_personal_information",
        href: personalInformationPath,
      },
      {
        id: "change_password",
        titleKey: "account_menu_change_password",
        href: passwordSecurityPath,
      },
      {
        id: "preferences",
        titleKey: "account_menu_preferences",
        href: myPreferencesPath,
      },
      {
        id: "privacy_policy",
        titleKey: "account_menu_privacy_policy",
        href: "/privacy",
        target: "_blank",
      },
      {
        id: "delete_account",
        titleKey: "account_menu_delete_account",
        onClick: () => {},
      },
    ],
    []
  );

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_account" />

      <div className="mb-8">
        <div className="space-y-6">
          <MenuItem itemList={accountMenuItems.slice(0, 3)} />
          <MenuItem itemList={accountMenuItems.slice(3)} />
        </div>
      </div>
    </StartAuthenticationHeader>
  );
};

export default Account;
