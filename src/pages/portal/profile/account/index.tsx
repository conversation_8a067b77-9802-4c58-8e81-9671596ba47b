import React, { useMemo, useState } from "react";

import Start<PERSON><PERSON>enticationHeader from "components/StartAuthentication/Header";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";
import {
  personalInformationPath,
  passwordSecurityPath,
  myPreferencesPath,
} from "components/StartAuthentication/constant";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import DeleteConfirmModal from "components/DeleteConfirmModal";

const Account = () => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteAccount = async () => {
    try {
      setIsDeleting(true);
      // TODO: Implement actual delete account API call
      // await apiCore.delete(null, "v1/me", {}, accessToken);
      console.log("Delete account functionality to be implemented");
      // After successful deletion, redirect to login or home
    } catch (error) {
      console.error("Error deleting account:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Account menu items configuration
  const accountMenuItems = useMemo(
    () => [
      {
        id: "personal_information",
        titleKey: "account_menu_personal_information",
        href: personalInformationPath,
      },
      {
        id: "change_password",
        titleKey: "account_menu_change_password",
        href: passwordSecurityPath,
      },
      {
        id: "preferences",
        titleKey: "account_menu_preferences",
        href: myPreferencesPath,
      },
      {
        id: "privacy_policy",
        titleKey: "account_menu_privacy_policy",
        href: "/privacy",
        target: "_blank",
      },
      {
        id: "delete_account",
        titleKey: "account_menu_delete_account",
        onClick: () => setIsDeleteModalOpen(true),
      },
    ],
    []
  );

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_account" />

      <div className="mb-8">
        <div className="space-y-6">
          <MenuItem itemList={accountMenuItems.slice(0, 3)} />
          <MenuItem itemList={accountMenuItems.slice(3)} />
        </div>
      </div>

      <DeleteConfirmModal
        isModalOpen={isDeleteModalOpen}
        setIsModalOpen={setIsDeleteModalOpen}
        title="确认删除账户?"
        content="删除账户是永久性的。您的服务请求、帖子和评论等数据将被永久删除。"
        confirmText="确认"
        cancelText="取消"
        onConfirm={handleDeleteAccount}
        loading={isDeleting}
      />
    </StartAuthenticationHeader>
  );
};

export default Account;
